name: Push branch workflow ghcr develop

on:
  push:
    branches:
      - develop

jobs:
  build-branch:
    name: Build branch and run unit tests
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.7

      - name: Setup Node.js
        uses: actions/setup-node@4.4.0
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Run unit tests
        run: npm run test:unit

      - name: Run e2e tests
        run: npm run e2e:run

  push-ghcr:
    name: Build branch and push ghcr.io
    runs-on: ubuntu-22.04
    needs: build-branch

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.7

      - name: Build Docker Image
        run: |
          docker build -t ghcr.io/sursindmitry/platform-frontend:develop-${{ github.sha }} \
                       -t ghcr.io/sursindmitry/platform-frontend:latest .

      - name: Log in to GitHub Container Registry
        run: echo "${{ secrets.TOKEN_GITHUB }}" | docker login ghcr.io -u ${{ secrets.USERNAME_GITHUB }} --password-stdin

      - name: Push Docker Image
        run: |
          docker push ghcr.io/sursindmitry/platform-frontend:develop-${{ github.sha }}
          docker push ghcr.io/sursindmitry/platform-frontend:latest
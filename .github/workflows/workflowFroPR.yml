name: Push branch workflow develop
on:
  push:
    branches:
      - 'feature/*'
      - 'bugfix/*'

jobs:
  build-and-test:
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.7

      - name: Setup Node.js
        uses: actions/setup-node@4.4.0
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Run unit tests
        run: npm run test:unit

      - name: Run e2e tests
        run: npm run e2e:run
{"extends": "@tsconfig/node22/tsconfig.json", "compilerOptions": {"composite": true, "noEmit": true, "tsBuildInfoFile": "../node_modules/.tmp/tsconfig.nightwatch.tsbuildinfo", "target": "ESNext", "module": "commonjs", "moduleResolution": "node", "rootDir": "../", "lib": ["ESNext", "dom"], "types": ["nightwatch"]}, "include": ["../node_modules/@nightwatch/**/*", "../src/**/*", "../tests/e2e/**/*"], "ts-node": {"transpileOnly": true}, "files": ["nightwatch.d.ts"]}
{"name": "platform-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "nuxi dev", "build": "nuxi build", "start": "nuxi preview", "test:unit": "vitest", "test:e2e": "nightwatch --env chrome tests/e2e/*", "e2e:run": "start-server-and-test dev http://localhost:3000 test:e2e", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@nuxt/icon": "^1.15.0", "@pinia/nuxt": "^0.11.2", "nuxt": "^4.0.1", "pinia": "^3.0.3"}, "devDependencies": {"@nightwatch/vue": "^3.1.2", "@nuxt/test-utils": "^3.19.2", "@pinia/testing": "^1.0.2", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/nightwatch": "^2.3.32", "@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "chromedriver": "^138.0.3", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "geckodriver": "^5.0.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "nightwatch": "^3.12.2", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "start-server-and-test": "^2.0.12", "ts-node": "^10.9.2", "typescript": "~5.8.3", "vite": "^6.3.5", "vite-plugin-nightwatch": "^0.4.6", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^3.0.3"}}
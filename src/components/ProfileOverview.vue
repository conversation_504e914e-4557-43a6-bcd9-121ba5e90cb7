<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useProfile } from '@/composables/useProfile'

const { profile, loading, errorMessage, loadProfile } = useProfile()

// Вычисляемые свойства для отображения
const fullName = computed(() => {
  if (!profile.value) return ''
  return `${profile.value.firstName} ${profile.value.lastName}`
})

const displayUsername = computed(() => {
  if (!profile.value?.username) return ''
  return `@${profile.value.username}`
})

const displayGithub = computed(() => {
  if (!profile.value?.github) return ''
  return `@${profile.value.github}`
})

const jobInfo = computed(() => {
  if (!profile.value) return ''
  const parts = []
  if (profile.value.position) parts.push(profile.value.position)
  if (profile.value.company) parts.push(`в ${profile.value.company}`)
  return parts.join(' ')
})

const experienceInfo = computed(() => {
  if (!profile.value?.experience) return ''
  return `Опыт: ${profile.value.experience}`
})

// Загружаем профиль при монтировании компонента
onMounted(() => {
  loadProfile()
})
</script>

<template>
  <div class="profile-overview">
    <!-- Состояние загрузки -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>Загрузка профиля...</p>
    </div>

    <!-- Состояние ошибки -->
    <div v-else-if="errorMessage" class="error-state">
      <p class="error-message">{{ errorMessage }}</p>
      <button @click="loadProfile" class="retry-button">Попробовать снова</button>
    </div>

    <!-- Основной контент профиля -->
    <div v-else-if="profile" class="profile-content">
      <div class="profile-header">
        <!-- Аватар -->
        <div class="avatar-container">
          <img
            v-if="profile.avatar"
            :src="profile.avatar"
            :alt="`Аватар ${fullName}`"
            class="avatar"
          />
          <div v-else class="avatar-placeholder">
            <Icon name="material-symbols:person" size="3rem" />
          </div>
        </div>

        <!-- Основная информация -->
        <div class="profile-info">
          <h1 class="profile-name">{{ fullName }}</h1>
          <div class="profile-details">
            <p v-if="displayUsername" class="username">{{ displayUsername }}</p>
            <p class="email">{{ profile.email }}</p>
          </div>
        </div>

        <!-- Кнопки действий -->
        <div class="profile-actions">
          <button class="btn btn-primary">
            <Icon name="material-symbols:add" size="1.2rem" />
            Создать проект
          </button>
          <button class="btn btn-secondary">
            <Icon name="material-symbols:edit" size="1.2rem" />
            Редактировать
          </button>
        </div>
      </div>

      <!-- Дополнительная информация -->
      <div class="profile-additional">
        <div class="info-item" v-if="profile.github">
          <Icon name="mdi:github" size="1.2rem" />
          <span>GitHub</span>
        </div>
        <div class="info-item" v-if="displayGithub">
          <Icon name="mdi:github" size="1.2rem" />
          <span>{{ displayGithub }}</span>
        </div>
        <div class="info-item" v-if="jobInfo">
          <Icon name="material-symbols:work" size="1.2rem" />
          <span>{{ jobInfo }}</span>
        </div>
        <div class="info-item" v-if="experienceInfo">
          <Icon name="material-symbols:schedule" size="1.2rem" />
          <span>{{ experienceInfo }}</span>
        </div>
      </div>
    </div>

    <!-- Состояние отсутствия данных -->
    <div v-else class="empty-state">
      <p>Профиль не найден</p>
    </div>
  </div>
</template>
<style scoped>
.profile-overview {
  background: #f8fffe;
  border-radius: 12px;
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

/* Состояния загрузки и ошибок */
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e0e0e0;
  border-top: 3px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  color: #e63946;
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.retry-button {
  background: #8b5cf6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

.retry-button:hover {
  background: #7c3aed;
}

/* Основной контент профиля */
.profile-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-header {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  flex-wrap: wrap;
}

/* Аватар */
.avatar-container {
  flex-shrink: 0;
}

.avatar,
.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

/* Информация о профиле */
.profile-info {
  flex: 1;
  min-width: 300px;
}

.profile-name {
  font-size: 2rem;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
  color: #1a1a1a;
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.username {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.email {
  color: #333;
  font-size: 1rem;
  margin: 0;
}

/* Кнопки действий */
.profile-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  border: none;
}

.btn-primary {
  background: #8b5cf6;
  color: white;
}

.btn-primary:hover {
  background: #7c3aed;
  transform: translateY(-1px);
}

.btn-secondary {
  background: white;
  color: #333;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

/* Дополнительная информация */
.profile-additional {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4b5563;
  font-size: 0.95rem;
}

.info-item svg {
  color: #6b7280;
}

/* Адаптивность */
@media (max-width: 768px) {
  .profile-overview {
    padding: 1.5rem;
  }

  .profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .profile-info {
    min-width: auto;
  }

  .profile-name {
    font-size: 1.75rem;
  }

  .profile-actions {
    justify-content: center;
  }

  .profile-additional {
    justify-content: center;
  }
}
</style>

import { ref } from 'vue'
import type { UserProfile, GetProfileRequest, UpdateProfileRequest } from '@/types/profile'
import { getProfile, updateProfile } from '@/services/ProfileService'
import { mapError } from '@/utils/ErrorMapper'

/**
 * Логика работы с профилем пользователя.
 */
export function useProfile() {
  const profile = ref<UserProfile | null>(null)
  const loading = ref(false)
  const updating = ref(false)
  const errorMessage = ref<string | null>(null)

  /**
   * Загружает профиль пользователя.
   *
   * @param request параметры запроса
   */
  const loadProfile = async (request: GetProfileRequest = {}) => {
    loading.value = true
    errorMessage.value = null

    try {
      // Временная заглушка для демонстрации
      // В реальном приложении здесь будет вызов API
      await new Promise(resolve => setTimeout(resolve, 1000)) // Имитация загрузки

      profile.value = {
        id: '1',
        firstName: 'Дмитрий',
        lastName: 'Сурсин',
        username: 'Phytonist123',
        email: '<EMAIL>',
        avatar: '', // Пустой аватар для демонстрации placeholder
        github: 'DmitrySursin',
        position: 'Middle Python Developer',
        company: 'ООО "Рога и копыта"',
        experience: '1 год 6 месяцев',
        bio: ''
      }

      // Раскомментировать для реального API:
      // const response = await getProfile(request)
      // profile.value = response
    } catch (err) {
      console.error('Ошибка при загрузке профиля:', err)
      const { general } = mapError(err)
      errorMessage.value = general
    } finally {
      loading.value = false
    }
  }

  /**
   * Обновляет профиль пользователя.
   *
   * @param request данные для обновления
   */
  const updateUserProfile = async (request: UpdateProfileRequest) => {
    updating.value = true
    errorMessage.value = null

    try {
      const response = await updateProfile(request)
      profile.value = response
      return response
    } catch (err) {
      console.error('Ошибка при обновлении профиля:', err)
      const { general } = mapError(err)
      errorMessage.value = general
      throw err
    } finally {
      updating.value = false
    }
  }

  /**
   * Очищает состояние ошибки.
   */
  const clearError = () => {
    errorMessage.value = null
  }

  return {
    profile,
    loading,
    updating,
    errorMessage,
    loadProfile,
    updateUserProfile,
    clearError,
  }
}

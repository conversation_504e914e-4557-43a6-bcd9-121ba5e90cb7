import { ref } from 'vue'
import type { RegisterRequest } from '@/types/register'
import { register } from '@/services/RegisterService'
import { mapError } from '@/utils/ErrorMapper'

/**
 * Логика регистрации.
 */
export function useRegister() {
  const firstName = ref('')
  const lastName = ref('')
  const email = ref('')
  const phone = ref('')
  const password = ref('')
  const loading = ref(false)
  const isSuccess = ref(false)
  const errorMessage = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string>>({})

  /**
   * Обработчик отправки формы регистрации.
   *
   * Выполняет запрос регистрации
   */
  const submit = async () => {
    loading.value = true
    errorMessage.value = null
    fieldErrors.value = {}

    const payload: RegisterRequest = {
      firstName: firstName.value,
      lastName: lastName.value,
      email: email.value,
      phone: phone.value,
      password: password.value,
    }

    try {
      const response = await register(payload)
      console.log(response)

      isSuccess.value = true
    } catch (err) {
      console.error('Ошибка при регистрации:', err)
      const {general, fields} = mapError(err)
      errorMessage.value = general
      fieldErrors.value = fields
    } finally {
      loading.value = false
    }
  }

  return {
    firstName,
    lastName,
    email,
    phone,
    password,
    loading,
    errorMessage,
    fieldErrors,
    submit,
    isSuccess
  }
}

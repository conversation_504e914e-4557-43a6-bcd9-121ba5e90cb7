<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { api } from '@/utils/Api'

const authStore = useAuthStore()
const data = ref<string>('')
const loading = ref<boolean>(true)
const error = ref<string>('')

onMounted(async () => {
  try {
    let response

    if (authStore.isAdmin) {
      response = await api<string>('/user-service/api/v1/admin/hello', {
        method: 'GET',
      })
      data.value = response
    }

    if (authStore.isModerator) {
      response = await api<string>('/user-service/api/v1/moderator/hello', {
        method: 'GET',
      })
      data.value = response
    }

    if (authStore.isManager) {
      response = await api<string>('/user-service/api/v1/manager/hello', {
        method: 'GET',
      })
      data.value = response
    }

    if (authStore.isUser) {
      response = await api<string>('/user-service/api/v1/user/hello', {
        method: 'GET',
      })
      data.value = response
    }
  } catch (e: unknown) {
    if (typeof e === 'string') {
      error.value = e
    } else if (e instanceof Error) {
      error.value = e.message
    } else {
      error.value = 'Неизвестная ошибка'
    }
    console.error('Ошибка:', error.value)
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <div>
    <div v-if="loading">Загрузка...</div>
    <div v-else-if="error">Ошибка: {{ error }}</div>
    <div v-else>Ответ: {{ data }}</div>
  </div>
</template>

<style scoped></style>

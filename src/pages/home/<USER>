<script setup lang="ts">
import { ref } from 'vue'

const count = ref<number>(0)

function increment() {
  count.value++
}

function decrement() {
  count.value--
}
</script>

<template>
  <ClientOnly>
    <h1 data-testid="increment-decrement-h1">{{ count }}</h1>
    <button data-testid="increment" @click="increment">+</button>
    <button data-testid="decrement" @click="decrement">-</button>
  </ClientOnly>
</template>

<script setup lang="ts">
import { useLogin } from '@/composables/useLogin'
import { ref } from 'vue'

const showPassword = ref(false)

const { email, password, loading, errorMessage, submit } = useLogin()

/**
 * Обработчик отправки формы.
 *
 * @param e событие отправки формы
 */
const handleSubmit = async (e: Event) => {
  e.preventDefault()

  await submit()
}
</script>

<template>
  <div class="page-container">
    <main class="login-wrapper">
      <form class="login-form" @submit="handleSubmit">
        <h1 class="form-title">Вход</h1>

        <div class="form-group">
          <label for="email">E-mail</label>
          <input
            id="email"
            type="email"
            v-model="email"
            placeholder="E-mail"
            autocomplete="email"
            required
          />
        </div>

        <div class="form-group">
          <label for="password">Пароль</label>
          <div class="input-with-icon">
            <input
              :type="showPassword ? 'text' : 'password'"
              id="password"
              v-model="password"
              placeholder="Пароль"
              autocomplete="current-password"
              required
            />
            <button type="button" class="toggle-password" @click="showPassword = !showPassword">
              <Icon
                :name="showPassword ? 'material-symbols:visibility-off' : 'material-symbols:visibility'"
                size="1.5rem"
              />
            </button>
          </div>
        </div>

        <div v-if="errorMessage" class="form-error">
          {{ errorMessage }}
        </div>

        <input
          class="submit-btn"
          type="submit"
          :disabled="loading"
          :value="loading ? 'Вход...' : 'Вход'"
        />
      </form>
    </main>
  </div>
</template>

<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
  max-height: 100vh;
}

.login-wrapper {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-form {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 25rem;
  gap: 1.5rem;
  margin: 0 auto;
  padding: 2rem;
}

.form-title {
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
}

.form-group {
  display: flex;
  align-items: center;
  position: relative;
  padding-bottom: 0.75rem;
}

.form-group::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: #ccc;
}

label {
  width: 6rem;
  text-align: left;
  font-size: 1rem;
  color: #27374d;
  flex-shrink: 0;
}

input {
  flex: 1;
  font-size: 1rem;
  padding: 0.5rem 0;
  border: none;
  outline: none;
  min-width: 0;
}

input::placeholder {
  color: #9db2bf;
}

.input-with-icon {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
}

.input-with-icon input {
  flex: 1;
  padding-right: 2.5rem;
}

.toggle-password {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  color: #666;
}

.submit-btn {
  margin-top: 1rem;
  padding: 0.75rem;
  border: none;
  border-radius: 0.5rem;
  background-color: #222;
  color: #fff;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submit-btn:hover {
  background-color: #393e46;
}
</style>

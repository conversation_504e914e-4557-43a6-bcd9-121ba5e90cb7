<script setup lang="ts">
import { useRegister } from '@/composables/useRegister'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const showPassword = ref(false)
const router = useRouter()

const {
  firstName,
  lastName,
  email,
  phone,
  password,
  loading,
  errorMessage,
  fieldErrors,
  submit,
  isSuccess,
} = useRegister()

/**
 * Обработчик отправки формы.
 *
 * @param e событие отправки формы
 */
const handleSubmit = async (e: Event) => {
  e.preventDefault()

  await submit()
}

/**
 * Обработчик нажатия на "ОК" в модальном окне после успешной регистрации.
 */
const handleOk = () => {
  router.push('/login')
}
</script>

<template>
  <div class="page-container">
    <main class="register-wrapper">
      <form class="register-form" @submit="handleSubmit">
        <h1 class="form-title">Регистрация</h1>
        <ErrorMessage :message="errorMessage" />
        <div class="form-group">
          <div class="input-container">
            <label for="firstName">Имя</label>
            <input
              id="firstName"
              type="text"
              v-model="firstName"
              placeholder="Имя"
              autocomplete="given-name"
              required
            />
          </div>
          <div class="error-container">
            <FieldError :error="fieldErrors['user.firstName.invalid']" />
          </div>
        </div>

        <div class="form-group">
          <div class="input-container">
            <label for="lastName">Фамилия</label>
            <input
              id="lastName"
              type="text"
              v-model="lastName"
              placeholder="Фамилия"
              autocomplete="family-name"
              required
            />
          </div>
          <div class="error-container">
            <FieldError :error="fieldErrors['user.lastName.invalid']" />
          </div>
        </div>

        <div class="form-group">
          <div class="input-container">
            <label for="email">E-mail</label>
            <input
              id="email"
              type="email"
              v-model="email"
              placeholder="E-mail"
              autocomplete="email"
              required
            />
          </div>
          <div class="error-container">
            <FieldError :error="fieldErrors['user.email.invalid']" />
            <FieldError :error="fieldErrors['message']" />
          </div>
        </div>

        <div class="form-group">
          <div class="input-container">
            <label for="phone">Телефон</label>
            <input
              id="phone"
              type="tel"
              v-model="phone"
              placeholder="Номер телефона"
              autocomplete="phone"
              required
            />
          </div>
          <div class="error-container">
            <FieldError :error="fieldErrors['user.phone.invalid']" />
            <FieldError :error="fieldErrors['message']" />
          </div>
        </div>

        <div class="form-group">
          <div class="input-container">
            <label for="password">Пароль</label>
            <div class="input-with-icon">
              <input
                :type="showPassword ? 'text' : 'password'"
                id="password"
                v-model="password"
                placeholder="Пароль"
                autocomplete="new-password"
                required
              />
              <button type="button" class="toggle-password" @click="showPassword = !showPassword">
                <Icon
                  :name="
                    showPassword ? 'material-symbols:visibility-off' : 'material-symbols:visibility'
                  "
                  size="1.5rem"
                />
              </button>
            </div>
          </div>
          <div class="error-container">
            <FieldError :error="fieldErrors['user.password.invalid']" />
          </div>
        </div>

        <input
          class="submit-btn"
          type="submit"
          :disabled="loading"
          :value="loading ? 'Регистрация...' : 'Регистрация'"
        />
      </form>
    </main>
    <div v-if="isSuccess" class="modal-overlay">
      <div class="modal-content">
        <h2>Регистрация прошла успешно</h2>
        <h3>Пожалуйста, подтвердите ваш аккаунт по ссылке, отправленной на вашу почту</h3>
        <button class="submit-btn" @click="handleOk">OK</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
  max-height: 100vh;
}

.register-wrapper {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.register-form {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 25rem;
  gap: 1.5rem;
  margin: 0 auto;
  padding: 2rem;
}

.form-title {
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
}

.form-group {
  display: flex;
  align-items: flex-start;
  position: relative;
  padding-bottom: 0.75rem;
  gap: 1rem;
}

.form-group::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: #ccc;
}

.input-container {
  display: flex;
  align-items: center;
  width: 350px;
  flex-shrink: 0;
}

.error-container {
  min-width: 200px;
  display: flex;
  align-items: center;
  padding-left: 0.5rem;
}

.input-container label {
  width: 6rem;
  text-align: left;
  font-size: 1rem;
  color: #27374d;
  flex-shrink: 0;
}

.input-container input {
  flex: 1;
  font-size: 1rem;
  padding: 0.5rem 0;
  border: none;
  outline: none;
  min-width: 0;
}

input::placeholder {
  color: #9db2bf;
}

.input-container .input-with-icon {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
}

.input-container .input-with-icon input {
  flex: 1;
  padding-right: 2.5rem;
}

.toggle-password {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  color: #666;
}

.submit-btn {
  margin-top: 1rem;
  padding: 0.75rem;
  border: none;
  border-radius: 0.5rem;
  background-color: #222;
  color: #fff;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submit-btn:hover {
  background-color: #393e46;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 1rem;
  border-radius: 0.7rem;
  text-align: center;
}
</style>

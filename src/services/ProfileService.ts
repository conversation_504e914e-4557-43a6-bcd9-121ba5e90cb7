import type { UserProfile, GetProfileRequest, UpdateProfileRequest } from '@/types/profile'
import { api } from '@/utils/Api'

/**
 * Получает профиль пользователя.
 *
 * @param request {@link GetProfileRequest}
 */
export async function getProfile(request: GetProfileRequest = {}): Promise<UserProfile> {
  const endpoint = request.userId 
    ? `/user-service/api/v1/user/profile/${request.userId}`
    : '/user-service/api/v1/user/profile'
    
  return api<UserProfile>(endpoint, {
    method: 'GET',
  })
}

/**
 * Обновляет профиль пользователя.
 *
 * @param request {@link UpdateProfileRequest}
 */
export async function updateProfile(request: UpdateProfileRequest): Promise<UserProfile> {
  return api<UserProfile>('/user-service/api/v1/user/profile', {
    method: 'PUT',
    body: request,
  })
}

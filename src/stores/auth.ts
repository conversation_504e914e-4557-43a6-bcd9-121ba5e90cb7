import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { AuthPayload, UserInfo } from '@/types/auth'

/**
 * Логика авторизации пользователя.
 *
 * Хранит роли и время жизни токенов
 */
export const useAuthStore = defineStore('auth', () => {
  const user = ref<UserInfo | null>(null)
  const accessTokenExpiresAt = ref<number | null>(null)
  const refreshTokenExpiresAt = ref<number | null>(null)

  const isAdmin = computed(() => user.value?.roles.includes('ROLE_ADMIN') ?? false)
  const isModerator = computed(() => user.value?.roles.includes('ROLE_MODERATOR') ?? false)
  const isManager = computed(() => user.value?.roles.includes('ROLE_MANAGER') ?? false)
  const isUser = computed(() => user.value?.roles.includes('ROLE_USER') ?? false)
  const isAuthenticated = computed(() => !!user.value)

  /**
   * Сохраняет информацию о текущей сессии пользователя.
   *
   * @param payload объект сессии {@link AuthPayload}
   */
  function setSession(payload: AuthPayload) {
    const now = Date.now()
    user.value = { roles: payload.roles }
    accessTokenExpiresAt.value = now + payload.accessTokenExpiresIn * 1000
    refreshTokenExpiresAt.value = now + payload.refreshTokenExpiresIn * 1000
  }

  /**
   * Удаляет информацию о текущей сессии пользователя.
   */
  function clearSession() {
    user.value = null
    accessTokenExpiresAt.value = null
    refreshTokenExpiresAt.value = null
  }

  return {
    user,
    accessTokenExpiresAt,
    refreshTokenExpiresAt,
    isAdmin,
    isModerator,
    isManager,
    isUser,
    isAuthenticated,
    setSession,
    clearSession,
  }
})

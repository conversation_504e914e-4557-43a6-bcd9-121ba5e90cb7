/**
 * Профиль пользователя.
 */
export interface UserProfile {
  id: string
  firstName: string
  lastName: string
  username: string
  email: string
  avatar?: string
  github?: string
  position?: string
  company?: string
  experience?: string
  bio?: string
}

/**
 * Запрос на получение профиля.
 */
export interface GetProfileRequest {
  userId?: string
}

/**
 * Запрос на обновление профиля.
 */
export interface UpdateProfileRequest {
  firstName?: string
  lastName?: string
  username?: string
  avatar?: string
  github?: string
  position?: string
  company?: string
  experience?: string
  bio?: string
}

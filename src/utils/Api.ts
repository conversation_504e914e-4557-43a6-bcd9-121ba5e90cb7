import { $fetch } from 'ofetch'

const baseURL = process.env.NUXT_PUBLIC_API_BASE_URL || 'http://localhost:9090'

/**
 * Универсальный способ отправки запросов.
 *
 * Использовать в сервисах
 * @param url ендпоинты
 * @param options настройки запроса (method, body, headers, params, timeout, retry)
 */
export async function api<T>(url: string, options: Record<string, unknown> = {}): Promise<T> {
  return $fetch<T>(url, {
    baseURL,
    credentials: 'include',
    ...options,
  })
}

import type { FetchError } from 'ofetch'

interface MappedError {
  general: string | null
  fields: Record<string, string>
}

/**
 * Универсальный маппер ошибок API.
 *
 * @param error ошибка
 * @returns объект с общим сообщением и ошибками по полям {@link MappedError}
 */
export function mapError(error: unknown): MappedError {
  const mapped: MappedError = {
    general: null,
    fields: {},
  }

  const err = error as FetchError
  const data = err?.response?._data
  const status = err?.response?.status

  if (!data || typeof data !== 'object') {
    mapped.general = 'Неизвестная ошибка. Попробуйте позже.'
    return mapped
  }

  // 400 — Validation Error
  if (status === 400 && data.errors) {
    mapped.general = 'Ошибка валидации'
    mapped.fields = data.errors
  }

  // 409 - Conflict Error
  else if (status === 409 && data.errors) {
    mapped.general = "Конфликт"
    mapped.fields = data.errors
  }

  // 500 — Ошибка сервера
  else if (status === 500 && data.message) {
    mapped.general = 'Ошибка на сервере: ' + data.message
  }

  // fallback
  else {
    mapped.general = data.message || 'Произошла неизвестная ошибка.'
  }

  return mapped
}

describe('WelcomePage e2e', function () {
  beforeEach(async function (browser) {
    await browser.url('http://localhost:3000/home')
    await browser.waitForElementVisible('[data-testid="increment-decrement-h1"]', 10000)
  })

  it('отображает 0 по умолчанию', async function (browser) {
    const text = await browser.getText('[data-testid="increment-decrement-h1"]')
    browser.assert.strictEqual(text, '0')
  })

  it('увеличивает счётчик при нажатии на "+"', async function (browser) {
    await browser.waitForElementVisible('[data-testid="increment"]', 5000)
    await browser.click('[data-testid="increment"]')
    await browser.waitForElementVisible('[data-testid="increment-decrement-h1"]', 5000)
    const text = await browser.getText('[data-testid="increment-decrement-h1"]')
    browser.assert.strictEqual(text, '1')
  })

  it('уменьшает счётчик при нажатии на "-"', async function (browser) {
    await browser.waitForElementVisible('[data-testid="decrement"]', 5000)
    await browser.click('[data-testid="decrement"]')
    await browser.waitForElementVisible('[data-testid="increment-decrement-h1"]', 5000)
    const text = await browser.getText('[data-testid="increment-decrement-h1"]')
    browser.assert.strictEqual(text, '-1')
  })

  after(async function (browser) {
    await browser.end()
  })
})

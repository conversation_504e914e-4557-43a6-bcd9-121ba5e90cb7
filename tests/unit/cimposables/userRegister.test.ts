import { useRegister } from '@/composables/useRegister'
import { register as mockRegister } from '@/services/RegisterService'
import { nextTick } from 'vue'

vi.mock('@/services/RegisterService', () => ({
  register: vi.fn(),
}))

describe('Register', () => {
  it('Успешно регистрирует пользователя', async () => {
    ;(mockRegister as any).mockResolvedValueOnce({ success: true })

    const {
      firstName,
      lastName,
      email,
      phone,
      password,
      loading,
      errorMessage,
      fieldErrors,
      submit,
      isSuccess,
    } = useRegister()

    firstName.value = 'John'
    lastName.value = 'Doe'
    email.value = '<EMAIL>'
    phone.value = '+79999999999'
    password.value = '123456789'

    await submit()
    await nextTick()

    expect(mockRegister).toHaveBeenLastCalledWith({
      firstName: '<PERSON>',
      lastName: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+79999999999',
      password: '123456789',
    })

    expect(isSuccess.value).toBe(true)
    expect(loading.value).toBe(false)
    expect(errorMessage.value).toBeNull()
    expect(fieldErrors.value).toEqual({})
  })

  it('Возвращает ошибку валидации', async () => {
    const error: any = {
      response: {
        status: 400,
        _data: {
          message: 'Ошибка валидации',
          errors: {
            'user.password.invalid': 'Поле «Пароль» должно содержать не менее 6 символов',
          },
        },
      },
    }

    ;(mockRegister as any).mockRejectedValueOnce(error)

    const {
      firstName,
      lastName,
      email,
      phone,
      password,
      loading,
      errorMessage,
      fieldErrors,
      submit,
      isSuccess,
    } = useRegister()

    password.value = '12345'

    await submit()
    await nextTick()

    expect(isSuccess.value).toBe(false)
    expect(loading.value).toBe(false)
    expect(errorMessage.value).toBe('Ошибка валидации')
    expect(fieldErrors.value).toEqual({
      'user.password.invalid': 'Поле «Пароль» должно содержать не менее 6 символов',
    })
  })

  it('Возвращает ошибку конфликта', async () => {
    const error: any = {
      response: {
        status: 409,
        _data: {
          message: 'Конфликт',
          errors: {
            'message': 'Email или телефон уже существует',
          },
        },
      },
    }
    ;(mockRegister as any).mockRejectedValueOnce(error)

    const {
      firstName,
      lastName,
      email,
      phone,
      password,
      loading,
      errorMessage,
      fieldErrors,
      submit,
      isSuccess,
    } = useRegister()

    email.value = '<EMAIL>'
    phone.value = '+79999999999'

    await submit()
    await nextTick()

    expect(isSuccess.value).toBe(false)
    expect(loading.value).toBe(false)
    expect(errorMessage.value).toBe('Конфликт')
    expect(fieldErrors.value).toEqual({
      'message': 'Email или телефон уже существует',
    })
  })
})

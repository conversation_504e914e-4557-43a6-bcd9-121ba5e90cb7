import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import ProfileOverview from '@/components/ProfileOverview.vue'
import { ref, type Ref } from 'vue'

let mockProfile: Ref<any>
let mockLoading: Ref<boolean>
let mockErrorMessage: Ref<string | null>
let mockLoadProfile: ReturnType<typeof vi.fn>

vi.mock('@/composables/useProfile', () => ({
  useProfile: () => ({
    profile: mockProfile,
    loading: mockLoading,
    errorMessage: mockErrorMessage,
    loadProfile: mockLoadProfile,
  }),
}))

describe('ProfileOverview', () => {
  beforeEach(() => {
    mockProfile = ref(null)
    mockLoading = ref(false)
    mockErrorMessage = ref(null)
    mockLoadProfile = vi.fn()
  })

  it('показывает состояние загрузки', () => {
    mockLoading.value = true

    const wrapper = mount(ProfileOverview, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          Icon: true,
        },
      },
    })

    expect(wrapper.find('.loading-state').exists()).toBe(true)
    expect(wrapper.text()).toContain('Загрузка профиля...')
  })

  it('показывает ошибку', () => {
    mockErrorMessage.value = 'Ошибка загрузки'

    const wrapper = mount(ProfileOverview, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          Icon: true,
        },
      },
    })

    expect(wrapper.find('.error-state').exists()).toBe(true)
    expect(wrapper.text()).toContain('Ошибка загрузки')
  })

  it('показывает профиль пользователя', () => {
    mockProfile.value = {
      id: '1',
      firstName: 'Дмитрий',
      lastName: 'Сурсин',
      username: 'Phytonist123',
      email: '<EMAIL>',
      github: 'DmitrySursin',
      position: 'Middle Python Developer',
      company: 'ООО "Рога и копыта"',
      experience: '1 год 6 месяцев',
    }

    const wrapper = mount(ProfileOverview, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          Icon: true,
        },
      },
    })

    expect(wrapper.find('.profile-content').exists()).toBe(true)
    expect(wrapper.text()).toContain('Дмитрий Сурсин')
    expect(wrapper.text()).toContain('@Phytonist123')
    expect(wrapper.text()).toContain('<EMAIL>')
    expect(wrapper.text()).toContain('@DmitrySursin')
    expect(wrapper.text()).toContain('Middle Python Developer в ООО "Рога и копыта"')
    expect(wrapper.text()).toContain('Опыт: 1 год 6 месяцев')
  })

  it('вызывает loadProfile при монтировании', () => {
    mount(ProfileOverview, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          Icon: true,
        },
      },
    })

    expect(mockLoadProfile).toHaveBeenCalledOnce()
  })

  it('показывает кнопки действий', () => {
    mockProfile.value = {
      id: '1',
      firstName: 'Дмитрий',
      lastName: 'Сурсин',
      username: 'Phytonist123',
      email: '<EMAIL>',
    }

    const wrapper = mount(ProfileOverview, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          Icon: true,
        },
      },
    })

    expect(wrapper.text()).toContain('Создать проект')
    expect(wrapper.text()).toContain('Редактировать')
  })
})

import { mount } from '@vue/test-utils'
import Home from '@/pages/home/<USER>'

describe('Home', () => {
  it('по умолчанию отображает 0', () => {
    const wrapper = mount(Home)
    expect(wrapper.text()).toContain('0')
  })

  it('увеличивает счётчик при нажатии на ' + '', async () => {
    const wrapper = mount(Home)
    const plusButton = wrapper.find('button:nth-child(2)')
    await plusButton.trigger('click')
    expect(wrapper.text()).toContain('1')
  })

  it('уменьшает счётчик при нажатии на "-"', async () => {
    const wrapper = mount(Home)
    const minusButton = wrapper.find('button:nth-child(3)')
    await minusButton.trigger('click')
    expect(wrapper.text()).toContain('-1')
  })
})
